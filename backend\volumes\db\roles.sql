-- Create roles for <PERSON><PERSON><PERSON>
-- This script creates the necessary roles for <PERSON><PERSON><PERSON> to function properly
-- All roles use the same password from POSTGRES_PASSWORD environment variable

\echo 'Creating Supabase roles...'

-- Create anon role (for anonymous access)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'anon') THEN
        CREATE ROLE anon NOLOGIN NOINHERIT;
        RAISE NOTICE 'Created anon role';
    ELSE
        RAISE NOTICE 'anon role already exists';
    END IF;
END
$$;

-- Create authenticated role (for authenticated users)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'authenticated') THEN
        CREATE ROLE authenticated NOLOGIN NOINHERIT;
        RAISE NOTICE 'Created authenticated role';
    ELSE
        RAISE NOTICE 'authenticated role already exists';
    END IF;
END
$$;

-- Create service_role (for service-level access)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'service_role') THEN
        CREATE ROLE service_role NOLOGIN NOINHERIT BYPASSRLS;
        RAISE NOTICE 'Created service_role';
    ELSE
        RAISE NOTICE 'service_role already exists';
    END IF;
END
$$;

-- Create authenticator role (used by PostgREST)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'authenticator') THEN
        EXECUTE format('CREATE ROLE authenticator NOINHERIT LOGIN PASSWORD %L', current_setting('custom.postgres_password'));
        RAISE NOTICE 'Created authenticator role';
    ELSE
        RAISE NOTICE 'authenticator role already exists';
        -- Ensure password is set correctly
        EXECUTE format('ALTER ROLE authenticator PASSWORD %L', current_setting('custom.postgres_password'));
    END IF;
END
$$;

-- Create supabase_auth_admin role (used by GoTrue/Auth service)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'supabase_auth_admin') THEN
        EXECUTE format('CREATE ROLE supabase_auth_admin NOINHERIT CREATEROLE LOGIN PASSWORD %L', current_setting('custom.postgres_password'));
        RAISE NOTICE 'Created supabase_auth_admin role';
    ELSE
        RAISE NOTICE 'supabase_auth_admin role already exists';
        -- Ensure password is set correctly
        EXECUTE format('ALTER ROLE supabase_auth_admin PASSWORD %L', current_setting('custom.postgres_password'));
    END IF;
END
$$;

-- Create supabase_storage_admin role (used by Storage service)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'supabase_storage_admin') THEN
        EXECUTE format('CREATE ROLE supabase_storage_admin NOINHERIT CREATEROLE LOGIN PASSWORD %L', current_setting('custom.postgres_password'));
        RAISE NOTICE 'Created supabase_storage_admin role';
    ELSE
        RAISE NOTICE 'supabase_storage_admin role already exists';
        -- Ensure password is set correctly
        EXECUTE format('ALTER ROLE supabase_storage_admin PASSWORD %L', current_setting('custom.postgres_password'));
    END IF;
END
$$;



-- Create supabase_admin role (used by Meta service and general admin tasks)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'supabase_admin') THEN
        EXECUTE format('CREATE ROLE supabase_admin SUPERUSER CREATEROLE CREATEDB REPLICATION BYPASSRLS LOGIN PASSWORD %L', current_setting('custom.postgres_password'));
        RAISE NOTICE 'Created supabase_admin role';
    ELSE
        RAISE NOTICE 'supabase_admin role already exists';
        -- Ensure password is set correctly
        EXECUTE format('ALTER ROLE supabase_admin PASSWORD %L', current_setting('custom.postgres_password'));
    END IF;
END
$$;

-- Grant role memberships for authenticator (used by PostgREST)
\echo 'Granting role memberships...'
GRANT anon TO authenticator;
GRANT authenticated TO authenticator;
GRANT service_role TO authenticator;

-- Grant admin roles necessary permissions
GRANT postgres TO supabase_admin;

-- Create and grant permissions on schemas
\echo 'Setting up schema permissions...'

-- Ensure public schema exists and grant usage
GRANT USAGE ON SCHEMA public TO anon, authenticated, service_role;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin, supabase_storage_admin, supabase_admin;

-- Grant permissions on all existing tables in public schema
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated, service_role;

-- Grant admin permissions on public schema
GRANT ALL ON ALL TABLES IN SCHEMA public TO supabase_auth_admin, supabase_storage_admin, supabase_admin;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO supabase_auth_admin, supabase_storage_admin, supabase_admin;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO supabase_auth_admin, supabase_storage_admin, supabase_admin;

-- Set default privileges for future objects in public schema
\echo 'Setting up default privileges...'
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO anon, authenticated, service_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO anon, authenticated, service_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO anon, authenticated, service_role;

-- Set default privileges for admin roles
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO supabase_auth_admin, supabase_storage_admin, supabase_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO supabase_auth_admin, supabase_storage_admin, supabase_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO supabase_auth_admin, supabase_storage_admin, supabase_admin;

-- Create auth schema if it doesn't exist and grant permissions
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.schemata WHERE schema_name = 'auth') THEN
        CREATE SCHEMA auth;
        RAISE NOTICE 'Created auth schema';
    ELSE
        RAISE NOTICE 'auth schema already exists';
    END IF;
END
$$;

GRANT USAGE ON SCHEMA auth TO supabase_auth_admin, supabase_admin;
GRANT ALL ON ALL TABLES IN SCHEMA auth TO supabase_auth_admin, supabase_admin;
GRANT ALL ON ALL SEQUENCES IN SCHEMA auth TO supabase_auth_admin, supabase_admin;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA auth TO supabase_auth_admin, supabase_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON TABLES TO supabase_auth_admin, supabase_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON SEQUENCES TO supabase_auth_admin, supabase_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON FUNCTIONS TO supabase_auth_admin, supabase_admin;

-- Make supabase_auth_admin the owner of the auth schema
ALTER SCHEMA auth OWNER TO supabase_auth_admin;

-- Transfer ownership of existing auth functions to supabase_auth_admin
DO $$
DECLARE
    func_record RECORD;
BEGIN
    FOR func_record IN
        SELECT p.proname, pg_get_function_identity_arguments(p.oid) as args
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'auth'
    LOOP
        BEGIN
            EXECUTE format('ALTER FUNCTION auth.%I(%s) OWNER TO supabase_auth_admin',
                          func_record.proname, func_record.args);
            RAISE NOTICE 'Transferred ownership of function auth.%s to supabase_auth_admin', func_record.proname;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Could not transfer ownership of function auth.%s: %s', func_record.proname, SQLERRM;
        END;
    END LOOP;
END
$$;

-- Ensure auth schema has proper permissions for migrations
GRANT CREATE ON SCHEMA auth TO supabase_auth_admin;
GRANT ALL PRIVILEGES ON SCHEMA auth TO supabase_auth_admin;

-- Create storage schema if it doesn't exist and grant permissions
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.schemata WHERE schema_name = 'storage') THEN
        CREATE SCHEMA storage;
        RAISE NOTICE 'Created storage schema';
    ELSE
        RAISE NOTICE 'storage schema already exists';
    END IF;
END
$$;

GRANT USAGE ON SCHEMA storage TO supabase_storage_admin, supabase_admin;
GRANT ALL ON ALL TABLES IN SCHEMA storage TO supabase_storage_admin, supabase_admin;
GRANT ALL ON ALL SEQUENCES IN SCHEMA storage TO supabase_storage_admin, supabase_admin;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA storage TO supabase_storage_admin, supabase_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA storage GRANT ALL ON TABLES TO supabase_storage_admin, supabase_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA storage GRANT ALL ON SEQUENCES TO supabase_storage_admin, supabase_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA storage GRANT ALL ON FUNCTIONS TO supabase_storage_admin, supabase_admin;



-- Grant database-level permissions
\echo 'Setting up database-level permissions...'
GRANT CONNECT ON DATABASE postgres TO anon, authenticated, service_role;
GRANT CONNECT ON DATABASE postgres TO supabase_auth_admin, supabase_storage_admin, supabase_admin;

-- Allow admin roles to create schemas
GRANT CREATE ON DATABASE postgres TO supabase_auth_admin, supabase_storage_admin, supabase_admin;

\echo 'Supabase roles setup completed successfully!'

-- Display created roles for verification
\echo 'Created roles:'
SELECT rolname, rolcanlogin, rolcreaterole, rolcreatedb, rolsuper
FROM pg_roles
WHERE rolname IN ('anon', 'authenticated', 'service_role', 'authenticator', 'supabase_auth_admin', 'supabase_storage_admin', 'supabase_admin')
ORDER BY rolname;
