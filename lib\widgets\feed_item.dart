import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../providers/posts_provider.dart';
import '../theme/app_theme.dart';
import '../screens/post_detail_screen.dart';

class FeedItem extends StatelessWidget {
  final PostModel post;

  const FeedItem({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // Background media content
          _buildMediaContent(),

          // Gradient overlay for better text readability
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.transparent,
                  Colors.black.withValues(alpha: 179), // 0.7 opacity
                ],
                stops: const [0.0, 0.6, 1.0],
              ),
            ),
          ),

          // Right side action buttons
          Positioned(
            right: 16,
            bottom: 100,
            child: Column(
              children: [
                _buildActionButton(
                  icon:
                      post.isLikedByCurrentUser
                          ? Icons.favorite
                          : Icons.favorite_border,
                  label: post.likeCount.toString(),
                  color: post.isLikedByCurrentUser ? Colors.red : Colors.white,
                  onPressed: () => _handleLike(context),
                ),
                const SizedBox(height: 24),
                _buildActionButton(
                  icon: Icons.chat_bubble_outline,
                  label: post.commentCount.toString(),
                  color: Colors.white,
                  onPressed: () => _handleComment(context),
                ),
                const SizedBox(height: 24),
                _buildActionButton(
                  icon: Icons.share,
                  label: 'Share',
                  color: Colors.white,
                  onPressed: () => _handleShare(context),
                ),
                const SizedBox(height: 24),
                _buildActionButton(
                  icon: Icons.more_vert,
                  label: '',
                  color: Colors.white,
                  onPressed: () => _showPostOptions(context),
                ),
              ],
            ),
          ),

          // Bottom content info
          Positioned(
            left: 16,
            right: 80,
            bottom: 100,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User info
                Row(
                  children: [
                    _buildAvatar(),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            post.authorDisplayName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            '@${post.authorUsername}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // Post content
                if (post.content.isNotEmpty)
                  Text(
                    post.content,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      height: 1.4,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaContent() {
    if (post.hasMedia && post.mediaUrl != null) {
      if (post.isImage) {
        return SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Image.network(
            post.mediaUrl!,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              if (kDebugMode) {
                print('Error loading image: $error');
              }
              return _buildMediaPlaceholder('Image');
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppColors.gfGreen,
                  ),
                ),
              );
            },
          ),
        );
      } else if (post.isVideo) {
        // For now, show a video placeholder
        return _buildMediaPlaceholder('Video');
      }
    }

    // Fallback to a gradient background
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
    );
  }

  Widget _buildMediaPlaceholder(String type) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == 'Video' ? Icons.play_circle_outline : Icons.image,
              size: 64,
              color: Colors.white70,
            ),
            const SizedBox(height: 16),
            Text(
              '$type Content',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    if (post.avatarUrl != null && post.avatarUrl!.isNotEmpty) {
      return CircleAvatar(
        backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
        radius: 20,
        backgroundImage: NetworkImage(post.avatarUrl!),
        onBackgroundImageError: (_, __) {},
        child:
            post.avatarUrl!.isEmpty
                ? Text(
                  post.authorDisplayName.isNotEmpty
                      ? post.authorDisplayName[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(
                    color: AppColors.gfDarkBlue,
                    fontWeight: FontWeight.bold,
                  ),
                )
                : null,
      );
    }

    return CircleAvatar(
      backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
      radius: 20,
      child: Text(
        post.authorDisplayName.isNotEmpty
            ? post.authorDisplayName[0].toUpperCase()
            : 'U',
        style: const TextStyle(
          color: AppColors.gfDarkBlue,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 128), // 0.5 opacity
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          if (label.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _handleLike(BuildContext context) async {
    final postsProvider = Provider.of<PostsProvider>(context, listen: false);
    await postsProvider.toggleLike(post.id);
  }

  void _handleComment(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
    );
  }

  void _handleShare(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
      ),
    );
  }

  void _showPostOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.gfDarkBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(
                    Icons.bookmark_border,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Save Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Save feature coming soon!'),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.report_outlined,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Report Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Report feature coming soon!'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }
}
