#!/usr/bin/env pwsh

# GameFlex WSL2 Docker Test Script
# This script tests all GameFlex services and functionality

param(
    [switch]$Verbose,
    [switch]$SkipDomains
)

$ErrorActionPreference = "Continue"

function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$ExpectedStatus = 200,
        [int]$TimeoutSeconds = 30
    )
    
    Write-Host "   Testing $ServiceName..." -NoNewline
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSeconds -UseBasicParsing -ErrorAction Stop
        if ($response.StatusCode -eq $ExpectedStatus) {
            Write-Host " ✅" -ForegroundColor Green
            return $true
        } else {
            Write-Host " ❌ (Status: $($response.StatusCode))" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host " ❌ (Error: $($_.Exception.Message))" -ForegroundColor Red
        if ($Verbose) {
            Write-Host "      Details: $($_.Exception)" -ForegroundColor Gray
        }
        return $false
    }
}

function Test-DatabaseConnection {
    param(
        [string]$Host,
        [int]$Port
    )
    
    Write-Host "   Testing Database Connection..." -NoNewline
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.ConnectAsync($Host, $Port).Wait(5000)
        if ($tcpClient.Connected) {
            Write-Host " ✅" -ForegroundColor Green
            $tcpClient.Close()
            return $true
        } else {
            Write-Host " ❌ (Connection failed)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host " ❌ (Error: $($_.Exception.Message))" -ForegroundColor Red
        return $false
    }
}

function Test-DockerContainers {
    Write-Host "🐳 Testing Docker Containers..." -ForegroundColor Yellow
    
    $containers = @(
        "supabase-studio",
        "supabase-kong", 
        "supabase-auth",
        "supabase-rest",
        "supabase-storage",
        "supabase-imgproxy",
        "supabase-meta",
        "supabase-realtime",
        "supabase-edge-functions",
        "supabase-db"
    )
    
    $allHealthy = $true
    
    foreach ($container in $containers) {
        Write-Host "   Checking $container..." -NoNewline
        
        try {
            $status = docker inspect $container --format='{{.State.Status}}' 2>$null
            if ($status -eq "running") {
                Write-Host " ✅ Running" -ForegroundColor Green
            } else {
                Write-Host " ❌ Status: $status" -ForegroundColor Red
                $allHealthy = $false
            }
        } catch {
            Write-Host " ❌ Not found" -ForegroundColor Red
            $allHealthy = $false
        }
    }
    
    return $allHealthy
}

function Test-NetworkConnectivity {
    param([bool]$UseDomains)
    
    Write-Host "🌐 Testing Network Connectivity..." -ForegroundColor Yellow
    
    if ($UseDomains) {
        $baseUrl = "http://api.gameflex.local:54321"
        $studioUrl = "http://studio.gameflex.local:54323"
        $dbHost = "db.gameflex.local"
    } else {
        $baseUrl = "http://localhost:54321"
        $studioUrl = "http://localhost:54323"
        $dbHost = "localhost"
    }
    
    $services = @(
        @{ Name = "Kong API Gateway"; Url = "$baseUrl/health"; Status = 200 },
        @{ Name = "Supabase Studio"; Url = "$studioUrl/api/profile"; Status = 200 },
        @{ Name = "Auth Service"; Url = "$baseUrl/auth/v1/health"; Status = 200 },
        @{ Name = "REST API"; Url = "$baseUrl/rest/v1/"; Status = 200 },
        @{ Name = "Storage API"; Url = "$baseUrl/storage/v1/status"; Status = 200 }
    )
    
    $allHealthy = $true
    
    foreach ($service in $services) {
        $result = Test-ServiceHealth -ServiceName $service.Name -Url $service.Url -ExpectedStatus $service.Status
        if (!$result) { $allHealthy = $false }
    }
    
    # Test database connection
    $dbResult = Test-DatabaseConnection -Host $dbHost -Port 54322
    if (!$dbResult) { $allHealthy = $false }
    
    return $allHealthy
}

function Test-Authentication {
    param([bool]$UseDomains)
    
    Write-Host "🔐 Testing Authentication..." -ForegroundColor Yellow
    
    if ($UseDomains) {
        $baseUrl = "http://api.gameflex.local:54321"
    } else {
        $baseUrl = "http://localhost:54321"
    }
    
    Write-Host "   Testing user signup..." -NoNewline
    
    try {
        $signupData = @{
            email = "test-$(Get-Random)@gameflex.com"
            password = "TestPassword123!"
        } | ConvertTo-Json
        
        $headers = @{
            "Content-Type" = "application/json"
            "apikey" = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/auth/v1/signup" -Method POST -Body $signupData -Headers $headers -TimeoutSec 10
        
        if ($response.user) {
            Write-Host " ✅" -ForegroundColor Green
            return $true
        } else {
            Write-Host " ❌ (No user returned)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host " ❌ (Error: $($_.Exception.Message))" -ForegroundColor Red
        return $false
    }
}

function Test-RealtimeWebSocket {
    param([bool]$UseDomains)
    
    Write-Host "⚡ Testing Realtime WebSocket..." -ForegroundColor Yellow
    
    if ($UseDomains) {
        $wsUrl = "ws://api.gameflex.local:54321/realtime/v1/websocket"
    } else {
        $wsUrl = "ws://localhost:54321/realtime/v1/websocket"
    }
    
    Write-Host "   WebSocket endpoint available..." -NoNewline
    
    # Simple check if the realtime endpoint responds
    try {
        if ($UseDomains) {
            $testUrl = "http://api.gameflex.local:54321/realtime/v1/"
        } else {
            $testUrl = "http://localhost:54321/realtime/v1/"
        }
        
        $response = Invoke-WebRequest -Uri $testUrl -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        Write-Host " ✅" -ForegroundColor Green
        return $true
    } catch {
        Write-Host " ❌ (Error: $($_.Exception.Message))" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host ""
Write-Host "🧪 GameFlex WSL2 Docker Test Suite" -ForegroundColor Cyan -BackgroundColor DarkBlue
Write-Host ""

# Check if Docker is running
Write-Host "🔍 Checking Docker..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "   ✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Docker is not running or not accessible" -ForegroundColor Red
    Write-Host "   Please start Docker Desktop and try again." -ForegroundColor Gray
    exit 1
}

# Check if containers are running
$containersHealthy = Test-DockerContainers
if (!$containersHealthy) {
    Write-Host ""
    Write-Host "❌ Some containers are not running. Please start the backend first:" -ForegroundColor Red
    Write-Host "   .\start.ps1" -ForegroundColor White
    exit 1
}

# Determine if we should test domains
$useDomains = $false
if (!$SkipDomains) {
    $hostsFile = "$env:SystemRoot\System32\drivers\etc\hosts"
    $hostsContent = Get-Content $hostsFile -ErrorAction SilentlyContinue
    if ($hostsContent -contains "# GameFlex Development - START") {
        $useDomains = $true
        Write-Host "🎯 Testing with domain names (*.gameflex.local)" -ForegroundColor Cyan
    } else {
        Write-Host "🎯 Testing with localhost URLs" -ForegroundColor Cyan
        Write-Host "   💡 Run .\setup-hosts-windows.ps1 as Administrator to enable domain names" -ForegroundColor Gray
    }
} else {
    Write-Host "🎯 Testing with localhost URLs (domains skipped)" -ForegroundColor Cyan
}

Write-Host ""

# Run tests
$allTestsPassed = $true

$networkResult = Test-NetworkConnectivity -UseDomains $useDomains
if (!$networkResult) { $allTestsPassed = $false }

Write-Host ""

$authResult = Test-Authentication -UseDomains $useDomains
if (!$authResult) { $allTestsPassed = $false }

Write-Host ""

$realtimeResult = Test-RealtimeWebSocket -UseDomains $useDomains
if (!$realtimeResult) { $allTestsPassed = $false }

# Final results
Write-Host ""
if ($allTestsPassed) {
    Write-Host "🎉 All tests passed! GameFlex backend is working correctly." -ForegroundColor Green -BackgroundColor DarkGreen
} else {
    Write-Host "❌ Some tests failed. Check the output above for details." -ForegroundColor Red -BackgroundColor DarkRed
}

Write-Host ""
Write-Host "🔧 Useful commands:" -ForegroundColor Magenta
Write-Host "   View logs: docker-compose logs -f" -ForegroundColor White
Write-Host "   Restart services: .\stop.ps1 && .\start.ps1" -ForegroundColor White
Write-Host "   Reset database: .\reset-database.ps1" -ForegroundColor White
Write-Host ""
