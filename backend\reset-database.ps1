#!/usr/bin/env pwsh
# PowerShell script to completely reset and reseed the Supabase database

Write-Host "Resetting Supabase Database..." -ForegroundColor Yellow

# Stop all services
Write-Host "Stopping Supabase services..." -ForegroundColor Blue
docker-compose down

# Remove the database volume to completely reset the database
Write-Host "Removing database volume..." -ForegroundColor Red
docker volume rm backend_db_data 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "Database volume removed successfully" -ForegroundColor Green
} else {
    Write-Host "Database volume didn't exist or was already removed" -ForegroundColor Yellow
}

# Remove any orphaned containers
Write-Host "Cleaning up orphaned containers..." -ForegroundColor Blue
docker-compose rm -f

# Start services (this will recreate the database with fresh data)
Write-Host "Starting Supabase services with fresh database..." -ForegroundColor Green
docker-compose up -d

# Wait for database to be ready
Write-Host "Waiting for database to be ready..." -ForegroundColor Blue
$maxAttempts = 30
$attempt = 0

do {
    $attempt++
    Start-Sleep -Seconds 2
    $dbReady = docker exec supabase-db pg_isready -U postgres -h localhost 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Database is ready!" -ForegroundColor Green
        break
    }
    Write-Host "Attempt $attempt/$maxAttempts - Database not ready yet..." -ForegroundColor Yellow
} while ($attempt -lt $maxAttempts)

if ($attempt -ge $maxAttempts) {
    Write-Host "Database failed to start after $maxAttempts attempts" -ForegroundColor Red
    exit 1
}

# Verify the seed data was loaded
Write-Host "Verifying seed data..." -ForegroundColor Blue
$postCount = docker exec supabase-db psql -U postgres -d postgres -t -c "SELECT COUNT(*) FROM posts;" 2>$null
if ($LASTEXITCODE -eq 0) {
    $postCount = $postCount.Trim()
    Write-Host "Found $postCount posts in database" -ForegroundColor Green

    # Show posts with media URLs
    Write-Host "Posts with media:" -ForegroundColor Blue
    docker exec supabase-db psql -U postgres -d postgres -c "SELECT id, content, media_url FROM posts WHERE media_url IS NOT NULL ORDER BY created_at LIMIT 5;"
} else {
    Write-Host "Failed to verify seed data" -ForegroundColor Red
}

# Show service status
Write-Host "Service Status:" -ForegroundColor Blue
docker-compose ps

Write-Host ""
Write-Host "Database reset complete!" -ForegroundColor Green
Write-Host "You can now restart your Flutter app to use the fresh database" -ForegroundColor Cyan
Write-Host "Supabase Studio: http://localhost:54323" -ForegroundColor Cyan
Write-Host "API URL: http://localhost:54321" -ForegroundColor Cyan
