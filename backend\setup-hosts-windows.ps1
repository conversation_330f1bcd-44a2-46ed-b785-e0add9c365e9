#!/usr/bin/env pwsh

# GameFlex WSL2 Docker Host Setup Script for Windows
# This script configures the Windows hosts file to resolve GameFlex domain names

param(
    [switch]$Remove,
    [switch]$Force
)

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "   Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

$hostsFile = "$env:SystemRoot\System32\drivers\etc\hosts"
$backupFile = "$env:SystemRoot\System32\drivers\etc\hosts.gameflex.backup"

# GameFlex domain entries
$gameflexEntries = @(
    "127.0.0.1 api.gameflex.local",
    "127.0.0.1 studio.gameflex.local", 
    "127.0.0.1 auth.gameflex.local",
    "127.0.0.1 rest.gameflex.local",
    "127.0.0.1 storage.gameflex.local",
    "127.0.0.1 imgproxy.gameflex.local",
    "127.0.0.1 meta.gameflex.local",
    "127.0.0.1 realtime.gameflex.local",
    "127.0.0.1 functions.gameflex.local",
    "127.0.0.1 db.gameflex.local",
    "127.0.0.1 postgres.gameflex.local",
    "127.0.0.1 kong.gameflex.local"
)

$startMarker = "# GameFlex Development - START"
$endMarker = "# GameFlex Development - END"

function Remove-GameFlexEntries {
    Write-Host "🗑️  Removing GameFlex entries from hosts file..." -ForegroundColor Yellow
    
    if (!(Test-Path $hostsFile)) {
        Write-Host "❌ Hosts file not found: $hostsFile" -ForegroundColor Red
        return
    }
    
    $content = Get-Content $hostsFile
    $newContent = @()
    $inGameFlexSection = $false
    
    foreach ($line in $content) {
        if ($line -eq $startMarker) {
            $inGameFlexSection = $true
            continue
        }
        if ($line -eq $endMarker) {
            $inGameFlexSection = $false
            continue
        }
        if (!$inGameFlexSection) {
            $newContent += $line
        }
    }
    
    $newContent | Set-Content $hostsFile -Encoding UTF8
    Write-Host "✅ GameFlex entries removed from hosts file" -ForegroundColor Green
}

function Add-GameFlexEntries {
    Write-Host "📝 Adding GameFlex entries to hosts file..." -ForegroundColor Yellow
    
    # Create backup if it doesn't exist
    if (!(Test-Path $backupFile)) {
        Write-Host "💾 Creating backup of hosts file..." -ForegroundColor Cyan
        Copy-Item $hostsFile $backupFile
    }
    
    # Check if entries already exist
    $content = Get-Content $hostsFile -ErrorAction SilentlyContinue
    if ($content -contains $startMarker) {
        if (!$Force) {
            Write-Host "⚠️  GameFlex entries already exist in hosts file!" -ForegroundColor Yellow
            Write-Host "   Use -Force to overwrite existing entries" -ForegroundColor Gray
            return
        } else {
            Write-Host "🔄 Removing existing entries..." -ForegroundColor Yellow
            Remove-GameFlexEntries
        }
    }
    
    # Add new entries
    $newEntries = @()
    $newEntries += ""
    $newEntries += $startMarker
    $newEntries += "# These entries allow GameFlex Docker containers to be accessed via domain names"
    $newEntries += "# Generated by setup-hosts-windows.ps1"
    $newEntries += $gameflexEntries
    $newEntries += $endMarker
    
    Add-Content $hostsFile $newEntries -Encoding UTF8
    Write-Host "✅ GameFlex entries added to hosts file" -ForegroundColor Green
}

function Test-GameFlexDomains {
    Write-Host "🔍 Testing GameFlex domain resolution..." -ForegroundColor Yellow
    
    $testDomains = @("api.gameflex.local", "studio.gameflex.local", "db.gameflex.local")
    $allGood = $true
    
    foreach ($domain in $testDomains) {
        try {
            $result = Resolve-DnsName $domain -ErrorAction Stop
            if ($result.IPAddress -eq "127.0.0.1") {
                Write-Host "   ✅ $domain → 127.0.0.1" -ForegroundColor Green
            } else {
                Write-Host "   ❌ $domain → $($result.IPAddress) (expected 127.0.0.1)" -ForegroundColor Red
                $allGood = $false
            }
        } catch {
            Write-Host "   ❌ $domain → Failed to resolve" -ForegroundColor Red
            $allGood = $false
        }
    }
    
    if ($allGood) {
        Write-Host "🎉 All GameFlex domains are resolving correctly!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Some domains are not resolving correctly. Check your hosts file." -ForegroundColor Yellow
    }
}

# Main execution
Write-Host ""
Write-Host "🎮 GameFlex WSL2 Docker Host Setup" -ForegroundColor Cyan -BackgroundColor DarkBlue
Write-Host ""

if ($Remove) {
    Remove-GameFlexEntries
    Write-Host ""
    Write-Host "🔧 To restore original hosts file:" -ForegroundColor Magenta
    Write-Host "   Copy-Item '$backupFile' '$hostsFile'" -ForegroundColor White
} else {
    Add-GameFlexEntries
    Write-Host ""
    Test-GameFlexDomains
    Write-Host ""
    Write-Host "🎯 GameFlex Services will be available at:" -ForegroundColor Cyan
    Write-Host "   📊 Supabase Studio: http://studio.gameflex.local:54323" -ForegroundColor White
    Write-Host "   🔌 API Gateway: http://api.gameflex.local:54321" -ForegroundColor White
    Write-Host "   🗄️  Database: db.gameflex.local:54322" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 To remove these entries later:" -ForegroundColor Magenta
    Write-Host "   .\setup-hosts-windows.ps1 -Remove" -ForegroundColor White
}

Write-Host ""
