#!/bin/bash

# GameFlex WSL2 Docker Test Script
# This script tests all GameFlex services and functionality

set -e

VERBOSE=false
SKIP_DOMAINS=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --skip-domains|-s)
            SKIP_DOMAINS=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [--verbose] [--skip-domains]"
            echo "  --verbose, -v       Show detailed error messages"
            echo "  --skip-domains, -s  Skip domain name testing, use localhost only"
            echo "  --help, -h          Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

test_service_health() {
    local service_name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    local timeout="${4:-30}"
    
    printf "   Testing %s..." "$service_name"
    
    if command -v curl >/dev/null 2>&1; then
        response=$(curl -s -o /dev/null -w "%{http_code}" --max-time "$timeout" "$url" 2>/dev/null || echo "000")
    elif command -v wget >/dev/null 2>&1; then
        response=$(wget -q -O /dev/null --timeout="$timeout" --server-response "$url" 2>&1 | grep "HTTP/" | tail -1 | awk '{print $2}' || echo "000")
    else
        echo " ❌ (No curl or wget available)"
        return 1
    fi
    
    if [[ "$response" == "$expected_status" ]]; then
        echo " ✅"
        return 0
    else
        echo " ❌ (Status: $response)"
        return 1
    fi
}

test_database_connection() {
    local host="$1"
    local port="$2"
    
    printf "   Testing Database Connection..."
    
    if command -v nc >/dev/null 2>&1; then
        if nc -z "$host" "$port" 2>/dev/null; then
            echo " ✅"
            return 0
        else
            echo " ❌ (Connection failed)"
            return 1
        fi
    elif command -v telnet >/dev/null 2>&1; then
        if timeout 5 telnet "$host" "$port" </dev/null >/dev/null 2>&1; then
            echo " ✅"
            return 0
        else
            echo " ❌ (Connection failed)"
            return 1
        fi
    else
        echo " ❌ (No nc or telnet available)"
        return 1
    fi
}

test_docker_containers() {
    echo "🐳 Testing Docker Containers..."
    
    local containers=(
        "supabase-studio"
        "supabase-kong"
        "supabase-auth"
        "supabase-rest"
        "supabase-storage"
        "supabase-imgproxy"
        "supabase-meta"
        "supabase-realtime"
        "supabase-edge-functions"
        "supabase-db"
    )
    
    local all_healthy=true
    
    for container in "${containers[@]}"; do
        printf "   Checking %s..." "$container"
        
        if status=$(docker inspect "$container" --format='{{.State.Status}}' 2>/dev/null); then
            if [[ "$status" == "running" ]]; then
                echo " ✅ Running"
            else
                echo " ❌ Status: $status"
                all_healthy=false
            fi
        else
            echo " ❌ Not found"
            all_healthy=false
        fi
    done
    
    [[ "$all_healthy" == "true" ]]
}

test_network_connectivity() {
    local use_domains="$1"
    
    echo "🌐 Testing Network Connectivity..."
    
    if [[ "$use_domains" == "true" ]]; then
        local base_url="http://api.gameflex.local:54321"
        local studio_url="http://studio.gameflex.local:54323"
        local db_host="db.gameflex.local"
    else
        local base_url="http://localhost:54321"
        local studio_url="http://localhost:54323"
        local db_host="localhost"
    fi
    
    local all_healthy=true
    
    # Test services
    test_service_health "Kong API Gateway" "$base_url/health" 200 || all_healthy=false
    test_service_health "Supabase Studio" "$studio_url/api/profile" 200 || all_healthy=false
    test_service_health "Auth Service" "$base_url/auth/v1/health" 200 || all_healthy=false
    test_service_health "REST API" "$base_url/rest/v1/" 200 || all_healthy=false
    test_service_health "Storage API" "$base_url/storage/v1/status" 200 || all_healthy=false
    
    # Test database connection
    test_database_connection "$db_host" 54322 || all_healthy=false
    
    [[ "$all_healthy" == "true" ]]
}

test_authentication() {
    local use_domains="$1"
    
    echo "🔐 Testing Authentication..."
    
    if [[ "$use_domains" == "true" ]]; then
        local base_url="http://api.gameflex.local:54321"
    else
        local base_url="http://localhost:54321"
    fi
    
    printf "   Testing user signup..."
    
    local test_email="test-$(date +%s)@gameflex.com"
    local signup_data="{\"email\":\"$test_email\",\"password\":\"TestPassword123!\"}"
    local anon_key="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
    
    if command -v curl >/dev/null 2>&1; then
        response=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -H "apikey: $anon_key" \
            -d "$signup_data" \
            --max-time 10 \
            "$base_url/auth/v1/signup" 2>/dev/null || echo "")
        
        if [[ "$response" == *"\"user\":"* ]]; then
            echo " ✅"
            return 0
        else
            echo " ❌ (No user returned)"
            return 1
        fi
    else
        echo " ❌ (No curl available)"
        return 1
    fi
}

test_realtime_websocket() {
    local use_domains="$1"
    
    echo "⚡ Testing Realtime WebSocket..."
    
    if [[ "$use_domains" == "true" ]]; then
        local test_url="http://api.gameflex.local:54321/realtime/v1/"
    else
        local test_url="http://localhost:54321/realtime/v1/"
    fi
    
    printf "   WebSocket endpoint available..."
    
    if test_service_health "" "$test_url" 200 5; then
        return 0
    else
        return 1
    fi
}

# Main execution
echo ""
echo "🧪 GameFlex WSL2 Docker Test Suite"
echo ""

# Check if Docker is running
echo "🔍 Checking Docker..."
if docker version >/dev/null 2>&1; then
    echo "   ✅ Docker is running"
else
    echo "   ❌ Docker is not running or not accessible"
    echo "   Please start Docker and try again."
    exit 1
fi

# Check if containers are running
if ! test_docker_containers; then
    echo ""
    echo "❌ Some containers are not running. Please start the backend first:"
    echo "   ./start.sh"
    exit 1
fi

# Determine if we should test domains
use_domains=false
if [[ "$SKIP_DOMAINS" != "true" ]]; then
    if grep -q "# GameFlex Development - START" /etc/hosts 2>/dev/null; then
        use_domains=true
        echo "🎯 Testing with domain names (*.gameflex.local)"
    else
        echo "🎯 Testing with localhost URLs"
        echo "   💡 Run 'sudo ./setup-hosts-linux.sh' to enable domain names"
    fi
else
    echo "🎯 Testing with localhost URLs (domains skipped)"
fi

echo ""

# Run tests
all_tests_passed=true

if ! test_network_connectivity "$use_domains"; then
    all_tests_passed=false
fi

echo ""

if ! test_authentication "$use_domains"; then
    all_tests_passed=false
fi

echo ""

if ! test_realtime_websocket "$use_domains"; then
    all_tests_passed=false
fi

# Final results
echo ""
if [[ "$all_tests_passed" == "true" ]]; then
    echo "🎉 All tests passed! GameFlex backend is working correctly."
else
    echo "❌ Some tests failed. Check the output above for details."
fi

echo ""
echo "🔧 Useful commands:"
echo "   View logs: docker-compose logs -f"
echo "   Restart services: ./stop.sh && ./start.sh"
echo "   Reset database: ./reset-database.sh"
echo ""
